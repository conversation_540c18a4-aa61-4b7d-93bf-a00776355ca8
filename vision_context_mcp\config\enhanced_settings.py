"""
Enhanced Configuration System with Validation and Runtime Updates
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

import structlog
from pydantic import BaseModel, Field, validator, root_validator
from pydantic.env_settings import BaseSettings

from ..core.providers.base import ProviderType

logger = structlog.get_logger(__name__)


class ProviderSettings(BaseModel):
    """Individual provider configuration"""
    enabled: bool = True
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model_name: Optional[str] = None
    max_tokens: int = Field(default=1000, ge=1, le=4000)
    timeout: float = Field(default=30.0, gt=0, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, gt=0, le=60)
    rate_limit_requests: int = Field(default=60, ge=1, le=1000)
    rate_limit_window: int = Field(default=60, ge=1, le=3600)
    priority: int = Field(default=1, ge=1, le=100)
    health_check_interval: float = Field(default=300.0, ge=30, le=3600)
    circuit_breaker_threshold: int = Field(default=5, ge=1, le=50)
    circuit_breaker_timeout: float = Field(default=60.0, ge=10, le=600)
    custom_headers: Dict[str, str] = Field(default_factory=dict)
    
    @validator('api_key')
    def validate_api_key(cls, v):
        if v and len(v.strip()) < 10:
            raise ValueError('API key appears to be too short')
        return v
    
    @validator('base_url')
    def validate_base_url(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('Base URL must start with http:// or https://')
        return v


class OptimizationSettings(BaseModel):
    """Performance optimization settings"""
    enable_caching: bool = True
    cache_ttl_seconds: int = Field(default=300, ge=60, le=3600)
    max_cache_size: int = Field(default=1000, ge=100, le=10000)
    
    enable_batching: bool = True
    max_batch_size: int = Field(default=5, ge=1, le=20)
    max_batch_wait_time: float = Field(default=2.0, ge=0.1, le=10.0)
    
    enable_image_optimization: bool = True
    max_image_width: int = Field(default=1920, ge=512, le=4096)
    max_image_height: int = Field(default=1080, ge=512, le=4096)
    image_quality: int = Field(default=85, ge=10, le=100)
    auto_format_selection: bool = True
    
    enable_connection_pooling: bool = True
    max_connections_per_provider: int = Field(default=10, ge=1, le=50)


class SecuritySettings(BaseModel):
    """Security and privacy settings"""
    api_key_encryption: bool = False
    log_sensitive_data: bool = False
    max_request_size_mb: int = Field(default=20, ge=1, le=100)
    rate_limit_global: int = Field(default=1000, ge=10, le=10000)
    rate_limit_window_global: int = Field(default=3600, ge=60, le=86400)


class MonitoringSettings(BaseModel):
    """Monitoring and logging settings"""
    log_level: str = Field(default="INFO", regex="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")
    log_file: Optional[str] = None
    enable_metrics: bool = True
    metrics_retention_days: int = Field(default=7, ge=1, le=30)
    health_check_interval: float = Field(default=60.0, ge=10, le=600)
    alert_on_provider_failure: bool = True
    alert_on_high_error_rate: bool = True
    error_rate_threshold: float = Field(default=0.1, ge=0.01, le=1.0)


class EnhancedSettings(BaseSettings):
    """Enhanced application settings with comprehensive configuration"""
    
    # Basic server settings
    server_host: str = Field(default="localhost", description="MCP server host")
    server_port: int = Field(default=8000, ge=1024, le=65535, description="MCP server port")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Provider configurations
    anthropic: ProviderSettings = Field(default_factory=ProviderSettings)
    openai: ProviderSettings = Field(default_factory=ProviderSettings)
    gemini: ProviderSettings = Field(default_factory=ProviderSettings)
    custom: ProviderSettings = Field(default_factory=ProviderSettings)
    
    # Default model selection with priority
    default_provider_priority: List[str] = Field(
        default=["custom", "anthropic", "openai", "gemini"],
        description="Provider priority order for auto-selection"
    )
    
    # Performance optimization
    optimization: OptimizationSettings = Field(default_factory=OptimizationSettings)
    
    # Security settings
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    
    # Monitoring settings
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    
    # Screen capture settings
    capture_quality: int = Field(default=95, ge=1, le=100)
    max_image_size: tuple = Field(default=(1920, 1080))
    capture_format: str = Field(default="PNG", regex="^(PNG|JPEG|WEBP)$")
    
    # Video recording settings
    video_fps: int = Field(default=30, ge=1, le=60)
    video_quality: int = Field(default=23, ge=0, le=51)
    max_recording_duration: int = Field(default=300, ge=1, le=1800)
    
    # Context engine settings
    context_check_interval: float = Field(default=1.0, ge=0.1, le=10.0)
    max_context_history: int = Field(default=100, ge=10, le=1000)
    
    class Config:
        env_file = ".env"
        env_prefix = "VISION_MCP_"
        case_sensitive = False
        validate_assignment = True
        extra = "forbid"  # Prevent unknown fields
    
    @root_validator
    def validate_provider_configs(cls, values):
        """Validate provider configurations"""
        # Ensure at least one provider is configured
        providers = ['anthropic', 'openai', 'gemini', 'custom']
        configured_providers = []
        
        for provider_name in providers:
            provider_config = values.get(provider_name)
            if provider_config and provider_config.enabled:
                if provider_name == 'custom':
                    if provider_config.base_url:
                        configured_providers.append(provider_name)
                else:
                    if provider_config.api_key:
                        configured_providers.append(provider_name)
        
        if not configured_providers:
            logger.warning("No providers are properly configured")
        
        return values
    
    @validator('default_provider_priority')
    def validate_provider_priority(cls, v):
        """Validate provider priority list"""
        valid_providers = {'anthropic', 'openai', 'gemini', 'custom'}
        for provider in v:
            if provider not in valid_providers:
                raise ValueError(f"Invalid provider in priority list: {provider}")
        return v
    
    def get_provider_config(self, provider_type: Union[str, ProviderType]) -> ProviderSettings:
        """Get configuration for a specific provider"""
        if isinstance(provider_type, ProviderType):
            provider_name = provider_type.value
        else:
            provider_name = provider_type.lower()
        
        provider_map = {
            'anthropic': self.anthropic,
            'openai': self.openai,
            'gemini': self.gemini,
            'custom': self.custom
        }
        
        return provider_map.get(provider_name, ProviderSettings())
    
    def get_enabled_providers(self) -> List[str]:
        """Get list of enabled providers"""
        enabled = []
        
        if self.anthropic.enabled and self.anthropic.api_key:
            enabled.append('anthropic')
        if self.openai.enabled and self.openai.api_key:
            enabled.append('openai')
        if self.gemini.enabled and self.gemini.api_key:
            enabled.append('gemini')
        if self.custom.enabled and self.custom.base_url:
            enabled.append('custom')
        
        return enabled
    
    def update_provider_config(self, provider: str, **kwargs) -> None:
        """Update provider configuration at runtime"""
        provider_config = getattr(self, provider, None)
        if provider_config:
            for key, value in kwargs.items():
                if hasattr(provider_config, key):
                    setattr(provider_config, key, value)
                    logger.info(
                        "Provider configuration updated",
                        provider=provider,
                        setting=key,
                        value=value if key != 'api_key' else '***'
                    )
    
    def export_config(self, include_secrets: bool = False) -> Dict[str, Any]:
        """Export configuration as dictionary"""
        config_dict = self.dict()
        
        if not include_secrets:
            # Remove sensitive information
            for provider in ['anthropic', 'openai', 'gemini', 'custom']:
                if provider in config_dict and 'api_key' in config_dict[provider]:
                    if config_dict[provider]['api_key']:
                        config_dict[provider]['api_key'] = '***'
        
        return config_dict
    
    def save_to_file(self, file_path: Union[str, Path], include_secrets: bool = False) -> None:
        """Save configuration to file"""
        config_dict = self.export_config(include_secrets=include_secrets)
        
        with open(file_path, 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        logger.info("Configuration saved to file", file_path=str(file_path))
    
    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> 'EnhancedSettings':
        """Load configuration from file"""
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        
        return cls(**config_dict)
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration and return status"""
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "provider_status": {}
        }
        
        # Check providers
        enabled_providers = self.get_enabled_providers()
        if not enabled_providers:
            validation_results["errors"].append("No providers are configured")
            validation_results["valid"] = False
        
        for provider in ['anthropic', 'openai', 'gemini', 'custom']:
            provider_config = getattr(self, provider)
            status = {"enabled": provider_config.enabled, "configured": False}
            
            if provider == 'custom':
                status["configured"] = bool(provider_config.base_url)
                if provider_config.enabled and not provider_config.base_url:
                    validation_results["warnings"].append(f"Custom provider enabled but no base_url configured")
            else:
                status["configured"] = bool(provider_config.api_key)
                if provider_config.enabled and not provider_config.api_key:
                    validation_results["warnings"].append(f"{provider} provider enabled but no API key configured")
            
            validation_results["provider_status"][provider] = status
        
        # Check optimization settings
        if self.optimization.enable_caching and self.optimization.max_cache_size < 100:
            validation_results["warnings"].append("Cache size is very small, may impact performance")
        
        if self.optimization.enable_batching and self.optimization.max_batch_size > 10:
            validation_results["warnings"].append("Large batch size may increase latency")
        
        # Security settings are minimal for MCP protocol (direct JSON-RPC communication)
        
        return validation_results


# Global settings instance
_settings_instance: Optional[EnhancedSettings] = None


def get_enhanced_settings() -> EnhancedSettings:
    """Get enhanced settings instance (singleton)"""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = EnhancedSettings()
    return _settings_instance


def reload_settings() -> EnhancedSettings:
    """Reload settings from environment/file"""
    global _settings_instance
    _settings_instance = EnhancedSettings()
    logger.info("Settings reloaded")
    return _settings_instance


def update_settings(**kwargs) -> None:
    """Update settings with new values"""
    settings = get_enhanced_settings()
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)
            logger.info("Setting updated", key=key, value=value)


class ConfigurationManager:
    """Configuration manager with validation and templates"""
    
    def __init__(self):
        self.logger = logger.bind(component="config_manager")
    
    def create_template_config(self, config_type: str = "basic") -> Dict[str, Any]:
        """Create configuration template"""
        templates = {
            "basic": {
                "anthropic": {"enabled": True, "api_key": "your_anthropic_key_here"},
                "openai": {"enabled": False, "api_key": "your_openai_key_here"},
                "optimization": {"enable_caching": True, "enable_batching": True},
                "monitoring": {"log_level": "INFO"}
            },
            "local": {
                "custom": {
                    "enabled": True,
                    "base_url": "http://localhost:11434/v1",
                    "model_name": "llava",
                    "api_key": "not-required"
                },
                "optimization": {"enable_caching": True, "enable_image_optimization": True},
                "monitoring": {"log_level": "DEBUG"}
            },
            "production": {
                "anthropic": {"enabled": True, "priority": 1},
                "openai": {"enabled": True, "priority": 2},
                "optimization": {
                    "enable_caching": True,
                    "enable_batching": True,
                    "enable_image_optimization": True
                },
                "security": {"api_key_encryption": True},
                "monitoring": {"log_level": "INFO", "enable_metrics": True}
            }
        }
        
        return templates.get(config_type, templates["basic"])
    
    def generate_env_file(self, config: Dict[str, Any], file_path: str = ".env") -> None:
        """Generate .env file from configuration"""
        env_lines = ["# Vision Context MCP Server Configuration", ""]
        
        def add_env_var(key: str, value: Any, comment: str = ""):
            if comment:
                env_lines.append(f"# {comment}")
            env_lines.append(f"VISION_MCP_{key.upper()}={value}")
            env_lines.append("")
        
        # Add provider configurations
        for provider, provider_config in config.items():
            if provider in ['anthropic', 'openai', 'gemini', 'custom']:
                env_lines.append(f"# {provider.title()} Provider Configuration")
                for key, value in provider_config.items():
                    env_key = f"{provider}_{key}"
                    add_env_var(env_key, value)
        
        # Write to file
        with open(file_path, 'w') as f:
            f.write('\n'.join(env_lines))
        
        self.logger.info("Environment file generated", file_path=file_path)
