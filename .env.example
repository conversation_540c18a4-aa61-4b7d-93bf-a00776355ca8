# 🧠 Vision Context MCP Server - Enhanced Configuration
# This file contains all configuration options for the enhanced AI model integration system

# ===== SERVER SETTINGS =====
VISION_MCP_SERVER_HOST=localhost
VISION_MCP_SERVER_PORT=8000
VISION_MCP_DEBUG=false

# ===== ANTHROPIC CLAUDE PROVIDER =====
VISION_MCP_ANTHROPIC_ENABLED=true
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_ANTHROPIC_MODEL_NAME=claude-3-5-sonnet-20241022
VISION_MCP_ANTHROPIC_MAX_TOKENS=1000
VISION_MCP_ANTHROPIC_TIMEOUT=30.0
VISION_MCP_ANTHROPIC_MAX_RETRIES=3
VISION_MCP_ANTHROPIC_RETRY_DELAY=1.0
VISION_MCP_ANTHROPIC_RATE_LIMIT_REQUESTS=60
VISION_MCP_ANTHROPIC_RATE_LIMIT_WINDOW=60
VISION_MCP_ANTHROPIC_PRIORITY=1
VISION_MCP_ANTHROPIC_HEALTH_CHECK_INTERVAL=300.0
VISION_MCP_ANTHROPIC_CIRCUIT_BREAKER_THRESHOLD=5
VISION_MCP_ANTHROPIC_CIRCUIT_BREAKER_TIMEOUT=60.0

# ===== OPENAI GPT-4V PROVIDER =====
VISION_MCP_OPENAI_ENABLED=true
VISION_MCP_OPENAI_API_KEY=your_openai_api_key_here
VISION_MCP_OPENAI_BASE_URL=
VISION_MCP_OPENAI_MODEL_NAME=gpt-4-vision-preview
VISION_MCP_OPENAI_MAX_TOKENS=1000
VISION_MCP_OPENAI_TIMEOUT=30.0
VISION_MCP_OPENAI_MAX_RETRIES=3
VISION_MCP_OPENAI_RETRY_DELAY=1.0
VISION_MCP_OPENAI_RATE_LIMIT_REQUESTS=60
VISION_MCP_OPENAI_RATE_LIMIT_WINDOW=60
VISION_MCP_OPENAI_PRIORITY=2
VISION_MCP_OPENAI_HEALTH_CHECK_INTERVAL=300.0
VISION_MCP_OPENAI_CIRCUIT_BREAKER_THRESHOLD=5
VISION_MCP_OPENAI_CIRCUIT_BREAKER_TIMEOUT=60.0

# ===== GOOGLE GEMINI PROVIDER =====
VISION_MCP_GEMINI_ENABLED=false
VISION_MCP_GEMINI_API_KEY=your_google_api_key_here
VISION_MCP_GEMINI_MODEL_NAME=gemini-pro-vision
VISION_MCP_GEMINI_MAX_TOKENS=1000
VISION_MCP_GEMINI_TIMEOUT=30.0
VISION_MCP_GEMINI_MAX_RETRIES=3
VISION_MCP_GEMINI_RETRY_DELAY=1.0
VISION_MCP_GEMINI_RATE_LIMIT_REQUESTS=60
VISION_MCP_GEMINI_RATE_LIMIT_WINDOW=60
VISION_MCP_GEMINI_PRIORITY=3
VISION_MCP_GEMINI_HEALTH_CHECK_INTERVAL=300.0
VISION_MCP_GEMINI_CIRCUIT_BREAKER_THRESHOLD=5
VISION_MCP_GEMINI_CIRCUIT_BREAKER_TIMEOUT=60.0

# ===== CUSTOM PROVIDER (OpenAI Compatible) =====
# Examples: Ollama, LocalAI, LM Studio, etc.
VISION_MCP_CUSTOM_ENABLED=false
VISION_MCP_CUSTOM_BASE_URL=http://localhost:11434/v1
VISION_MCP_CUSTOM_API_KEY=not-required
VISION_MCP_CUSTOM_MODEL_NAME=llava
VISION_MCP_CUSTOM_MAX_TOKENS=1000
VISION_MCP_CUSTOM_TIMEOUT=45.0
VISION_MCP_CUSTOM_MAX_RETRIES=2
VISION_MCP_CUSTOM_RETRY_DELAY=2.0
VISION_MCP_CUSTOM_RATE_LIMIT_REQUESTS=30
VISION_MCP_CUSTOM_RATE_LIMIT_WINDOW=60
VISION_MCP_CUSTOM_PRIORITY=4
VISION_MCP_CUSTOM_HEALTH_CHECK_INTERVAL=300.0
VISION_MCP_CUSTOM_CIRCUIT_BREAKER_THRESHOLD=3
VISION_MCP_CUSTOM_CIRCUIT_BREAKER_TIMEOUT=120.0

# Popular Custom Provider Examples:
# Ollama: http://localhost:11434/v1
# LocalAI: http://localhost:8080/v1
# LM Studio: http://localhost:1234/v1
# Oobabooga: http://localhost:5000/v1

# ===== PROVIDER PRIORITY =====
# Comma-separated list of provider names in priority order
VISION_MCP_DEFAULT_PROVIDER_PRIORITY=custom,anthropic,openai,gemini

# ===== PERFORMANCE OPTIMIZATION =====
VISION_MCP_OPTIMIZATION_ENABLE_CACHING=true
VISION_MCP_OPTIMIZATION_CACHE_TTL_SECONDS=300
VISION_MCP_OPTIMIZATION_MAX_CACHE_SIZE=1000
VISION_MCP_OPTIMIZATION_ENABLE_BATCHING=true
VISION_MCP_OPTIMIZATION_MAX_BATCH_SIZE=5
VISION_MCP_OPTIMIZATION_MAX_BATCH_WAIT_TIME=2.0
VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION=true
VISION_MCP_OPTIMIZATION_MAX_IMAGE_WIDTH=1920
VISION_MCP_OPTIMIZATION_MAX_IMAGE_HEIGHT=1080
VISION_MCP_OPTIMIZATION_IMAGE_QUALITY=85
VISION_MCP_OPTIMIZATION_AUTO_FORMAT_SELECTION=true
VISION_MCP_OPTIMIZATION_ENABLE_CONNECTION_POOLING=true
VISION_MCP_OPTIMIZATION_MAX_CONNECTIONS_PER_PROVIDER=10

# ===== SECURITY SETTINGS =====
VISION_MCP_SECURITY_API_KEY_ENCRYPTION=false
VISION_MCP_SECURITY_LOG_SENSITIVE_DATA=false
VISION_MCP_SECURITY_MAX_REQUEST_SIZE_MB=20
VISION_MCP_SECURITY_RATE_LIMIT_GLOBAL=1000
VISION_MCP_SECURITY_RATE_LIMIT_WINDOW_GLOBAL=3600

# ===== MONITORING & LOGGING =====
VISION_MCP_MONITORING_LOG_LEVEL=INFO
VISION_MCP_MONITORING_LOG_FILE=
VISION_MCP_MONITORING_ENABLE_METRICS=true
VISION_MCP_MONITORING_METRICS_RETENTION_DAYS=7
VISION_MCP_MONITORING_HEALTH_CHECK_INTERVAL=60.0
VISION_MCP_MONITORING_ALERT_ON_PROVIDER_FAILURE=true
VISION_MCP_MONITORING_ALERT_ON_HIGH_ERROR_RATE=true
VISION_MCP_MONITORING_ERROR_RATE_THRESHOLD=0.1

# ===== SCREEN CAPTURE SETTINGS =====
VISION_MCP_CAPTURE_QUALITY=95
VISION_MCP_MAX_IMAGE_SIZE=1920,1080
VISION_MCP_CAPTURE_FORMAT=PNG

# ===== VIDEO RECORDING SETTINGS =====
VISION_MCP_VIDEO_FPS=30
VISION_MCP_VIDEO_QUALITY=23
VISION_MCP_MAX_RECORDING_DURATION=300

# ===== CONTEXT ENGINE SETTINGS =====
VISION_MCP_CONTEXT_CHECK_INTERVAL=1.0
VISION_MCP_MAX_CONTEXT_HISTORY=100

# ===== CONFIGURATION EXAMPLES =====
#
# For Local Development (Ollama):
# VISION_MCP_CUSTOM_ENABLED=true
# VISION_MCP_CUSTOM_BASE_URL=http://localhost:11434/v1
# VISION_MCP_CUSTOM_MODEL_NAME=llava
# VISION_MCP_ANTHROPIC_ENABLED=false
# VISION_MCP_OPENAI_ENABLED=false
#
# For Production (Cloud APIs):
# VISION_MCP_ANTHROPIC_ENABLED=true
# VISION_MCP_OPENAI_ENABLED=true
# VISION_MCP_CUSTOM_ENABLED=false
# VISION_MCP_SECURITY_REQUIRE_AUTH=true
# VISION_MCP_MONITORING_LOG_LEVEL=INFO
#
# For High Performance:
# VISION_MCP_OPTIMIZATION_ENABLE_CACHING=true
# VISION_MCP_OPTIMIZATION_MAX_CACHE_SIZE=5000
# VISION_MCP_OPTIMIZATION_ENABLE_BATCHING=true
# VISION_MCP_OPTIMIZATION_MAX_BATCH_SIZE=10
# VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION=true
