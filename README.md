# 🧠 Vision Context MCP Server - Enhanced AI Model Integration

A comprehensive Model Context Protocol (MCP) server that provides advanced real-time visual perception capabilities to AI models. This enhanced system features sophisticated AI model integration with performance optimization, resilience features, and enterprise-grade reliability.

## ✨ Enhanced Features

### 🚀 **Advanced AI Model Integration**
- **Multi-Provider Support**: Anthropic Claude, OpenAI GPT-4V, Google Gemini, Custom OpenAI-compatible APIs
- **Intelligent Load Balancing**: Automatic provider selection with priority-based routing
- **Circuit Breaker Pattern**: Prevents cascade failures with automatic recovery
- **Comprehensive Error Handling**: Retry mechanisms, fallback strategies, detailed error reporting

### ⚡ **Performance Optimization**
- **Smart Image Compression**: Automatic optimization with target size control
- **Request Batching**: Efficient processing of multiple requests
- **Advanced Caching**: Intelligent caching with TTL and size management
- **Connection Pooling**: Optimized HTTP connections for better performance

### 🛡️ **Enterprise Reliability**
- **Health Monitoring**: Real-time provider health checks and status reporting
- **Rate Limiting**: Configurable rate limits per provider and globally
- **Security Features**: API key encryption, domain restrictions, request size limits
- **Comprehensive Logging**: Structured logging with metrics and alerting

### 🎯 **Core Vision Capabilities**
- **Real-time Screen Analysis**: Capture and analyze fullscreen or active window content
- **Video Recording**: Record screen activity for specified durations (1-300 seconds)
- **Background Context Engine**: Monitors window changes, application switches, and system events
- **MCP Protocol Compliance**: Full JSON-RPC 2.0 implementation for Claude Desktop integration
- **Cross-platform Support**: Works on Windows, macOS, and Linux
- **Conversation Context**: Maintains chat history with provider and performance metadata

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd vision

# Install dependencies
pip install -e .

# Or run the setup script
python setup.py
```

### Configuration

1. **Copy and configure environment file:**
```bash
cp .env.example .env
```

2. **Choose your configuration approach:**

#### Option A: Cloud APIs (Recommended for Production)
```env
# Enable Anthropic Claude (Primary)
VISION_MCP_ANTHROPIC_ENABLED=true
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_ANTHROPIC_PRIORITY=1

# Enable OpenAI GPT-4V (Fallback)
VISION_MCP_OPENAI_ENABLED=true
VISION_MCP_OPENAI_API_KEY=your_openai_api_key_here
VISION_MCP_OPENAI_PRIORITY=2

# Enable performance optimizations
VISION_MCP_OPTIMIZATION_ENABLE_CACHING=true
VISION_MCP_OPTIMIZATION_ENABLE_BATCHING=true
VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION=true
```

#### Option B: Local Models (Development/Privacy)
```env
# Enable Ollama (Primary)
VISION_MCP_CUSTOM_ENABLED=true
VISION_MCP_CUSTOM_BASE_URL=http://localhost:11434/v1
VISION_MCP_CUSTOM_MODEL_NAME=llava
VISION_MCP_CUSTOM_PRIORITY=1

# Fallback to cloud if needed
VISION_MCP_ANTHROPIC_ENABLED=true
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_ANTHROPIC_PRIORITY=2
```

#### Option C: High-Performance Setup
```env
# Multiple providers with optimizations
VISION_MCP_ANTHROPIC_ENABLED=true
VISION_MCP_OPENAI_ENABLED=true
VISION_MCP_CUSTOM_ENABLED=true

# Advanced optimizations
VISION_MCP_OPTIMIZATION_MAX_CACHE_SIZE=5000
VISION_MCP_OPTIMIZATION_MAX_BATCH_SIZE=10
VISION_MCP_OPTIMIZATION_ENABLE_CONNECTION_POOLING=true

# Enhanced monitoring
VISION_MCP_MONITORING_ENABLE_METRICS=true
VISION_MCP_MONITORING_ALERT_ON_PROVIDER_FAILURE=true
```

### Testing & Validation

```bash
# Test configuration
python -c "
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings
settings = get_enhanced_settings()
result = settings.validate_configuration()
print('✅ Valid' if result['valid'] else '❌ Invalid')
print('Errors:', result['errors'])
print('Warnings:', result['warnings'])
"

# Run comprehensive tests
python test_server.py

# Start the enhanced MCP server
python -m vision_context_mcp
```

## 📦 Enhanced Project Structure

```
vision_context_mcp/
├── __init__.py
├── server.py                    # Main MCP server implementation
├── tools/                       # MCP tool implementations
│   ├── __init__.py
│   ├── base.py                 # Base tool class
│   ├── screen_analyzer.py      # Enhanced screen analysis tool
│   ├── video_recorder.py       # Video recording tool
│   ├── vision_query.py         # Vision query tool
│   └── model_info.py           # Model information tool
├── core/                        # Core modules
│   ├── __init__.py
│   ├── enhanced_vision_llm.py  # 🆕 Enhanced Vision LLM with all features
│   ├── vision_llm.py           # Legacy Vision LLM (deprecated)
│   ├── screen_capture.py       # Screen capture functionality
│   ├── context_engine.py       # Background context monitoring
│   ├── providers/              # 🆕 AI Provider Management
│   │   ├── __init__.py
│   │   ├── base.py            # Base provider classes
│   │   ├── anthropic_provider.py
│   │   ├── openai_provider.py
│   │   ├── gemini_provider.py
│   │   ├── custom_provider.py
│   │   └── provider_manager.py # Provider orchestration
│   ├── optimization/           # 🆕 Performance Optimization
│   │   ├── __init__.py
│   │   ├── image_optimizer.py  # Smart image compression
│   │   ├── request_batcher.py  # Request batching
│   │   ├── cache_manager.py    # Advanced caching
│   │   └── connection_pool.py  # Connection pooling
│   └── resilience/             # 🆕 Error Handling & Resilience
│       ├── __init__.py
│       ├── error_handler.py    # Comprehensive error handling
│       ├── retry_manager.py    # Retry mechanisms
│       ├── circuit_breaker.py  # Circuit breaker pattern
│       ├── fallback_manager.py # Fallback strategies
│       └── health_monitor.py   # Health monitoring
├── config/
│   ├── __init__.py
│   ├── settings.py             # Legacy settings (deprecated)
│   └── enhanced_settings.py    # 🆕 Enhanced configuration system
├── utils/
│   ├── __init__.py
│   └── helpers.py              # Utility functions
└── docs/                       # 🆕 Comprehensive Documentation
    ├── ENHANCED_CONFIGURATION_GUIDE.md
    ├── ENHANCED_API_USAGE_GUIDE.md
    └── TROUBLESHOOTING.md
```

## 🔧 Claude Desktop Integration

### Add to Claude Desktop Configuration

Add the following to your Claude Desktop `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "vision-context-mcp": {
      "command": "python",
      "args": ["-m", "vision_context_mcp.server"],
      "cwd": "/path/to/your/vision/directory",
      "env": {
        "VISION_MCP_DEBUG": "false",
        "VISION_MCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### Configuration File Locations

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

## 🛠️ Enhanced MCP Tools

### 1. `analyze_screen_context` - Enhanced Screen Analysis
Captures and analyzes screen content with advanced optimization and provider selection.

**Enhanced Parameters:**
- `mode` (required): `"fullscreen"` or `"active_window"`
- `analysis_prompt` (optional): Custom analysis prompt
- `preferred_model` (optional): Provider name (e.g., "Anthropic Claude", "OpenAI GPT-4V")
- `optimize_image` (optional): Enable image optimization (default: true)
- `target_size_kb` (optional): Target image size in KB for optimization
- `use_cache` (optional): Enable caching (default: true)

**Example:**
```json
{
  "mode": "active_window",
  "analysis_prompt": "Analyze this application interface and identify any usability issues",
  "preferred_model": "Anthropic Claude",
  "optimize_image": true,
  "target_size_kb": 500,
  "use_cache": true
}
```

**Enhanced Response:**
```json
{
  "analysis": "Detailed analysis result...",
  "metadata": {
    "provider_used": "Anthropic Claude",
    "processing_time_seconds": 2.34,
    "optimization_applied": true,
    "original_size_kb": 1200,
    "optimized_size_kb": 480,
    "cache_hit": false
  }
}
```

### 2. `record_screen` - Enhanced Video Recording
Records screen activity with analysis and optimization.

**Enhanced Parameters:**
- `duration` (required): Recording duration in seconds (1-300)
- `mode` (optional): `"fullscreen"` or `"active_window"` (default: "fullscreen")
- `analyze_content` (optional): Analyze video content (default: true)
- `preferred_model` (optional): Provider for analysis

**Example:**
```json
{
  "duration": 15,
  "mode": "fullscreen",
  "analyze_content": true,
  "preferred_model": "OpenAI GPT-4V"
}
```

### 3. `query_vision_about_current_view` - Enhanced Vision Query
Asks questions about current screen with conversation context.

**Enhanced Parameters:**
- `question` (required): Question to ask about the current view
- `context` (optional): Additional context for the query
- `preferred_model` (optional): Provider name
- `use_conversation_history` (optional): Include conversation context (default: true)
- `optimize_image` (optional): Enable image optimization (default: true)

**Example:**
```json
{
  "question": "What errors do you see and how can I fix them?",
  "context": "I'm debugging a React application",
  "preferred_model": "Anthropic Claude",
  "use_conversation_history": true,
  "optimize_image": true
}
```

### 4. `get_available_models` - 🆕 Model Information
Get detailed information about available AI models and their status.

**Parameters:**
- `include_metrics` (optional): Include performance metrics (default: true)
- `check_health` (optional): Perform health check (default: false)

**Example:**
```json
{
  "include_metrics": true,
  "check_health": true
}
```

**Response:**
```json
{
  "providers": [
    {
      "name": "Anthropic Claude",
      "status": "healthy",
      "model": "claude-3-5-sonnet-20241022",
      "priority": 1,
      "metrics": {
        "success_rate": 98.5,
        "avg_response_time": 2.1,
        "total_requests": 1250
      }
    }
  ],
  "system_status": {
    "healthy": true,
    "cache_hit_rate": 0.35,
    "optimization_enabled": true
  }
}
```

## 🔍 Technical Architecture

### Core Components

1. **MCP Server**: JSON-RPC 2.0 compliant server handling tool calls from Claude Desktop
2. **Screen Capture Module**: Cross-platform screen capture using `mss`, `pygetwindow`, and platform-specific APIs
3. **Vision LLM Integration**: Unified interface for multiple Vision LLM providers
4. **Context Engine**: Background monitoring system for window changes and system events
5. **Tool Framework**: Extensible tool system for adding new visual capabilities

### Supported Platforms

- **Windows**: Uses `mss` for screen capture, `pygetwindow` for window management
- **macOS**: Uses `screencapture` command and Quartz framework
- **Linux**: Uses `xwininfo`, `import` command, and X11 libraries

### Vision LLM Providers

- **Anthropic Claude**: Claude 3.5 Sonnet with vision capabilities
- **OpenAI GPT-4V**: GPT-4 Vision Preview model
- **Google Gemini**: Gemini Pro Vision model
- **Custom Provider**: OpenAI-compatible API endpoints (Ollama, LocalAI, LM Studio, etc.)

## 🔧 Enhanced Claude Desktop Integration

### 1. Add to Claude Desktop Configuration

Edit your Claude Desktop configuration file:

**Windows:** `%APPDATA%\Claude\claude_desktop_config.json`
**macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`
**Linux:** `~/.config/claude/claude_desktop_config.json`

Add the enhanced MCP server configuration:

```json
{
  "mcpServers": {
    "vision-context-enhanced": {
      "command": "python",
      "args": ["-m", "vision_context_mcp"],
      "cwd": "/path/to/your/vision/directory",
      "env": {
        "VISION_MCP_ANTHROPIC_API_KEY": "your_anthropic_key_here",
        "VISION_MCP_OPTIMIZATION_ENABLE_CACHING": "true",
        "VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION": "true",
        "VISION_MCP_MONITORING_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### 2. Restart Claude Desktop

After adding the configuration, restart Claude Desktop to load the enhanced MCP server.

### 3. Verify Enhanced Integration

In Claude Desktop, you can now use enhanced commands like:
- "Analyze my current screen with optimization"
- "What's in the active window? Use the best available model"
- "Record my screen for 15 seconds and analyze the content"
- "What AI models are available and what's their status?"
- "Show me system performance metrics"

### 4. Advanced Usage Examples

```
🔍 Enhanced Screen Analysis:
"Analyze this screen and optimize the image for faster processing"

🎯 Provider Selection:
"Use Anthropic Claude to analyze this window"

📊 System Monitoring:
"Show me the health status of all AI providers"

⚡ Performance Optimization:
"Analyze this image with caching enabled and target size of 300KB"
```

## ⚙️ Enhanced Configuration Options

All configuration can be set via environment variables or `.env` file. See [Enhanced Configuration Guide](docs/ENHANCED_CONFIGURATION_GUIDE.md) for complete details.

### Core Provider Settings
- `VISION_MCP_ANTHROPIC_ENABLED`: Enable Anthropic Claude (default: true)
- `VISION_MCP_ANTHROPIC_API_KEY`: Anthropic API key
- `VISION_MCP_OPENAI_ENABLED`: Enable OpenAI GPT-4V (default: true)
- `VISION_MCP_OPENAI_API_KEY`: OpenAI API key
- `VISION_MCP_CUSTOM_ENABLED`: Enable custom provider (default: false)
- `VISION_MCP_CUSTOM_BASE_URL`: Custom API base URL

### Performance Optimization
- `VISION_MCP_OPTIMIZATION_ENABLE_CACHING`: Enable caching (default: true)
- `VISION_MCP_OPTIMIZATION_ENABLE_BATCHING`: Enable request batching (default: true)
- `VISION_MCP_OPTIMIZATION_ENABLE_IMAGE_OPTIMIZATION`: Enable image optimization (default: true)
- `VISION_MCP_OPTIMIZATION_MAX_CACHE_SIZE`: Maximum cache size (default: 1000)

### Security & Monitoring
- `VISION_MCP_SECURITY_API_KEY_ENCRYPTION`: Enable API key encryption (default: false)
- `VISION_MCP_SECURITY_LOG_SENSITIVE_DATA`: Log sensitive data (default: false)
- `VISION_MCP_MONITORING_LOG_LEVEL`: Log level (default: INFO)
- `VISION_MCP_MONITORING_ENABLE_METRICS`: Enable metrics collection (default: true)

## 🏠 Local Model Setup

### Ollama Setup

1. Install Ollama from [ollama.ai](https://ollama.ai)
2. Pull a vision model:
   ```bash
   ollama pull llava
   ```
3. Configure in `.env`:
   ```env
   VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:11434/v1
   VISION_MCP_CUSTOM_MODEL_NAME=llava
   VISION_MCP_CUSTOM_PROVIDER_NAME=Ollama
   ```

### LocalAI Setup

1. Install LocalAI from [localai.io](https://localai.io)
2. Configure a vision model in LocalAI
3. Configure in `.env`:
   ```env
   VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:8080/v1
   VISION_MCP_CUSTOM_MODEL_NAME=gpt-4-vision-preview
   VISION_MCP_CUSTOM_PROVIDER_NAME=LocalAI
   ```

### LM Studio Setup

1. Install LM Studio from [lmstudio.ai](https://lmstudio.ai)
2. Load a vision-capable model
3. Start the local server
4. Configure in `.env`:
   ```env
   VISION_MCP_CUSTOM_API_BASE_URL=http://localhost:1234/v1
   VISION_MCP_CUSTOM_MODEL_NAME=your-model-name
   VISION_MCP_CUSTOM_PROVIDER_NAME=LM Studio
   ```

## 🧪 Development

### Running Tests

```bash
# Run all tests
python test_server.py

# Test specific components
python -c "from vision_context_mcp.core.screen_capture import ScreenCapture; import asyncio; asyncio.run(ScreenCapture().capture_fullscreen())"
```

### Adding New Tools

1. Create a new tool class inheriting from `BaseTool`
2. Implement the `execute` method
3. Add tool registration in `server.py`
4. Update tool imports in `tools/__init__.py`

### Debugging

Enable debug mode for detailed logging:
```bash
export VISION_MCP_DEBUG=true
export VISION_MCP_LOG_LEVEL=DEBUG
python -m vision_context_mcp
```

## 🔒 Security & Privacy

- **Local Processing**: All screen capture and analysis happens locally
- **API Communication**: Only base64-encoded images are sent to Vision LLM APIs
- **No Data Storage**: No persistent storage of captured images or videos
- **Sandboxed Execution**: MCP server runs in isolated environment
- **Permission-based**: Requires explicit user permission through Claude Desktop

## 🐛 Enhanced Troubleshooting

### Configuration Validation

First, validate your configuration:
```bash
python -c "
from vision_context_mcp.config.enhanced_settings import get_enhanced_settings
settings = get_enhanced_settings()
result = settings.validate_configuration()
print('✅ Valid' if result['valid'] else '❌ Invalid')
print('Errors:', result['errors'])
print('Warnings:', result['warnings'])
"
```

### Common Issues

1. **No providers configured**
   - Ensure at least one provider has valid credentials
   - Check API key format and permissions
   - Verify custom provider base URL is accessible

2. **High error rates**
   - Check provider status: `python -c "from vision_context_mcp.core.enhanced_vision_llm import EnhancedVisionLLM; import asyncio; asyncio.run(EnhancedVisionLLM().health_check())"`
   - Verify network connectivity
   - Review circuit breaker settings

3. **Slow performance**
   - Enable image optimization in configuration
   - Increase cache size
   - Adjust batch settings
   - Check provider response times

4. **Screen capture fails on Linux**: Install required X11 dependencies
   ```bash
   sudo apt-get install python3-xlib imagemagick
   ```

5. **Claude Desktop not detecting server**: Verify configuration file path and syntax

### Enhanced Logs

Check comprehensive logs for debugging:
- Server logs: Console output with structured logging
- Configuration validation: Automatic validation on startup
- Provider health: Real-time health monitoring
- Performance metrics: Response times and success rates

### System Status Check

Get comprehensive system status:
```bash
python -c "
from vision_context_mcp.core.enhanced_vision_llm import EnhancedVisionLLM
import asyncio
async def check():
    llm = EnhancedVisionLLM()
    await llm.initialize()
    status = await llm.get_system_status()
    print('System Status:', status)
asyncio.run(check())
"
```

## 📚 Documentation

- **[Enhanced Configuration Guide](docs/ENHANCED_CONFIGURATION_GUIDE.md)**: Comprehensive configuration options and examples
- **[Enhanced API Usage Guide](docs/ENHANCED_API_USAGE_GUIDE.md)**: Detailed API usage with advanced features
- **[Troubleshooting Guide](docs/TROUBLESHOOTING.md)**: Common issues and solutions

## 🆕 What's New in v2.0

### 🚀 Enhanced AI Model Integration
- **Multi-Provider Support**: Seamless integration with Anthropic, OpenAI, Google, and custom providers
- **Intelligent Load Balancing**: Automatic provider selection with priority-based routing
- **Circuit Breaker Pattern**: Prevents cascade failures with automatic recovery

### ⚡ Performance Optimization
- **Smart Image Compression**: Automatic optimization with target size control
- **Request Batching**: Efficient processing of multiple requests
- **Advanced Caching**: Intelligent caching with TTL and size management
- **Connection Pooling**: Optimized HTTP connections for better performance

### 🛡️ Enterprise Reliability
- **Health Monitoring**: Real-time provider health checks and status reporting
- **Comprehensive Error Handling**: Retry mechanisms, fallback strategies, detailed error reporting
- **Rate Limiting**: Configurable rate limits per provider and globally
- **Security Features**: API key encryption, domain restrictions, request size limits

### 📊 Monitoring & Analytics
- **Performance Metrics**: Response times, success rates, error statistics
- **System Status**: Real-time system health and provider status
- **Conversation Context**: Enhanced conversation history with metadata
- **Comprehensive Logging**: Structured logging with metrics and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Development Setup

```bash
# Clone and setup development environment
git clone <repository-url>
cd vision
pip install -e ".[dev]"

# Run tests
python -m pytest tests/

# Run enhanced tests
python test_server.py
```

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- **[Model Context Protocol](https://github.com/modelcontextprotocol)** for the MCP specification and ecosystem
- **[Anthropic](https://anthropic.com)** for Claude AI and comprehensive MCP support
- **[OpenAI](https://openai.com)** for GPT-4V API and vision capabilities
- **[Google](https://ai.google.dev)** for Gemini Pro Vision API
- **Open Source Community** for essential libraries:
  - `mss`, `opencv-python`, `pygetwindow`, `Pillow` for screen capture
  - `structlog`, `pydantic`, `asyncio` for robust architecture
  - `httpx`, `aiohttp` for async HTTP operations
  - `pytest`, `black`, `mypy` for development tools

## 🌟 Features Roadmap

### Planned Enhancements
- **Multi-Modal Support**: Audio analysis and speech-to-text integration
- **Advanced Analytics**: ML-powered usage analytics and optimization suggestions
- **Plugin System**: Extensible plugin architecture for custom providers
- **Real-time Collaboration**: Multi-user screen sharing and analysis
- **Mobile Support**: iOS and Android companion apps

### Community Requests
- **Custom Model Training**: Fine-tuning support for domain-specific models
- **Advanced Security**: End-to-end encryption and enterprise SSO
- **API Gateway**: RESTful API for external integrations
- **Dashboard UI**: Web-based management and monitoring interface

---

**🚀 Ready to enhance your AI vision capabilities? Get started with the Enhanced Vision Context MCP Server today!**
